import 'dart:convert';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient
import 'package:wicker/services/auth_service.dart'; // For AuthService
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart'; // Add this import at the top

class TransactionService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;
  final AuthService _auth = AuthService();

  // In the TransactionService class, replace the initiateTransaction method

  Future<String> initiateTransaction({
    required String productId,
    String? bookingId, // <-- NEW: Optional bookingId
  }) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      // --- NEW: Add bookingId to the request body if it exists ---
      final body = {'product_id': productId};
      if (bookingId != null) {
        body['booking_id'] = bookingId;
      }

      final response = await _client.post(
        Uri.parse('$baseUrl/api/transactions/initiate'),
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        return responseBody['authorization_url'];
      } else {
        final responseBody = jsonDecode(response.body);
        throw Exception(
          responseBody['msg'] ?? 'Failed to initiate transaction',
        );
      }
    } catch (e) {
      print('Transaction service error: $e');
      rethrow;
    }
  }

  Future<String> initiateCartCheckout({
    required double deliveryFee,
    required LatLng deliveryLocation,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.post(
        Uri.parse('$baseUrl/api/transactions/initiate-cart-checkout'),
        body: jsonEncode({
          'delivery_fee': deliveryFee,
          'delivery_location': {
            'lat': deliveryLocation.latitude,
            'lon': deliveryLocation.longitude,
          },
        }),
      );

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        return responseBody['authorization_url'];
      } else {
        final responseBody = jsonDecode(response.body);
        throw Exception(
          responseBody['msg'] ?? 'Failed to initiate cart checkout',
        );
      }
    } catch (e) {
      print('Cart checkout service error: $e');
      rethrow;
    }
  }

  Future<void> uploadBuyerProof(String orderId, XFile image) async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth
        .getAccessToken(); // Assuming _auth is an AuthService instance in your class

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/transactions/order/$orderId/buyer-proof'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.files.add(
      await http.MultipartFile.fromPath('buyer_proof_image', image.path),
    );

    final response = await request.send();
    if (response.statusCode != 200) {
      // It's useful to read the response body on error
      final responseBody = await response.stream.bytesToString();
      final decodedBody = jsonDecode(responseBody);
      throw Exception(decodedBody['msg'] ?? 'Failed to upload proof');
    }
  }

  Future<Map<String, dynamic>> getMySales({
    String? searchQuery,
    String? status,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final queryParameters = {
        if (searchQuery != null && searchQuery.isNotEmpty)
          'search': searchQuery,
        if (status != null && status != 'all') 'status': status,
      };

      final uri = Uri.parse(
        '$baseUrl/api/transactions/my-sales',
      ).replace(queryParameters: queryParameters);

      final response = await _client.get(uri);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else if (response.statusCode == 404) {
        return {'sales_data': [], 'summary_stats': []};
      } else {
        throw Exception('Failed to load sales history');
      }
    } catch (e) {
      print('Get sales service error: $e');
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getMyOrders({
    String? searchQuery,
    String? sortBy,
    String? sortOrder,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final queryParameters = {
        if (searchQuery != null && searchQuery.isNotEmpty)
          'search': searchQuery,
        if (sortBy != null) 'sort_by': sortBy,
        if (sortOrder != null) 'sort_order': sortOrder,
      };

      final uri = Uri.parse(
        '$baseUrl/api/transactions/my-orders',
      ).replace(queryParameters: queryParameters);

      final response = await _client.get(uri);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('Failed to load orders');
      }
    } catch (e) {
      print('Get orders service error: $e');
      rethrow;
    }
  }

  Future<void> markAsDelivered(String orderId, XFile proofImage) async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth
        .getAccessToken(); // Assuming you have AuthService instance named _auth

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/transactions/order/$orderId/mark-delivered'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.files.add(
      await http.MultipartFile.fromPath('proof_image', proofImage.path),
    );

    final response = await request.send();
    if (response.statusCode != 200) {
      throw Exception('Failed to mark order as delivered');
    }
  }

  Future<void> confirmReceipt(String orderId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/transactions/order/$orderId/confirm-receipt'),
    );

    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to confirm receipt');
    }
  }
}
